"""
A federated learning server using FedBuff.

Reference:

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al., "Federated Learning with Buffered Asynchronous Aggregation,
" in Proc. International Conference on Artificial Intelligence and Statistics (AISTATS 2022).

https://proceedings.mlr.press/v151/nguyen22b/nguyen22b.pdf
"""
import asyncio
import logging
import statistics
import time
import os
import pickle
from datetime import datetime
import pandas as pd
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

from plato.config import Config
from plato.servers import fedavg
from plato.utils import csv_processor

from Origin_trainer import Origin_trainer
from Origin_client import Origin_client



class Server(fedavg.Server):
    """A federated learning server using the FedAsync algorithm."""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )
        self.current_round_losses = []
        self.avg_loss = 0.0
        
        # 添加陈旧度跟踪
        self.client_staleness = {}  # 记录每个客户端的陈旧度
        self.client_starting_rounds = {}  # 记录每个客户端的起始轮次
        self.staleness_history = []
        self.accuracy_log = []
        self.first_reach_time = {0.7: None, 0.8: None, 0.9: None}
        self.start_time = time.time()
        self.run_id = "fedbuff_metrics"
        
        # 获取结果保存路径
        result_path = getattr(Config(), 'result_path', os.path.join(os.getcwd(), 'results', 'fedbuff_metrics'))
        os.makedirs(result_path, exist_ok=True)
        
        # 初始化CSV表头
        csv_processor.initialize_csv(f"{self.run_id}.csv", ['round', 'elapsed_time', 'accuracy', 'avg_staleness'], result_path)
        
        # 创建时间戳文件名
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        self.csv_filename = f"fedbuff_metrics_{self.timestamp}.csv"
        self.csv_path = os.path.join(result_path, self.csv_filename)
        
        # 初始化CSV文件
        csv_processor.initialize_csv(
            self.csv_path,
            ['round', 'elapsed_time', 'accuracy', 'avg_staleness'],
            result_path=result_path
        )
        logging.info(f"结果将保存到: {self.csv_path}")

    async def select_clients(self):
        """选择客户端并发送当前轮次信息"""
        clients = await super().select_clients()
        
        # 在发送给客户端的消息中包含当前轮次信息
        for client in clients:
            client.server_response['current_round'] = self.current_round
            logging.info(f"向客户端 {client.client_id} 发送当前轮次信息: {self.current_round}")
        
        return clients

    async def process_reports(self):
        """处理客户端报告，更新客户端起始轮次信息"""
        await super().process_reports()
        
        # 更新客户端起始轮次信息
        for update in self.updates:
            client_id = update.client_id
            report = update.report
            
            # 记录客户端起始轮次
            if hasattr(report, 'starting_round'):
                self.client_starting_rounds[client_id] = report.starting_round
                
                # 计算并更新客户端陈旧度
                staleness = self.current_round - report.starting_round
                self.client_staleness[client_id] = staleness
                logging.info(f"客户端 {client_id} 的陈旧度为 {staleness} (当前轮次: {self.current_round}, 起始轮次: {report.starting_round})")
            else:
                logging.warning(f"客户端 {client_id} 的报告中没有起始轮次信息")

    async def _client_report_arrived(self, sid, client_id, report):
        """接收客户端报告时处理起始轮次信息"""
        # 首先调用父类的方法
        await super()._client_report_arrived(sid, client_id, report)
        
        # 从报告中获取起始轮次信息
        if hasattr(self.reports[sid], 'starting_round'):
            starting_round = self.reports[sid].starting_round
            self.client_starting_rounds[client_id] = starting_round
            staleness = self.current_round - starting_round
            self.client_staleness[client_id] = staleness
            logging.info(f"从报告中获取客户端 {client_id} 的起始轮次: {starting_round}, 陈旧度: {staleness}")
        else:
            logging.warning(f"客户端 {client_id} 的报告中没有起始轮次信息")

    async def process_client_info(self, client_id, sid):
        """处理客户端信息，记录起始轮次"""
        # 首先调用父类的方法
        await super().process_client_info(client_id, sid)
        
        # 从报告中获取起始轮次信息
        if hasattr(self.reports[sid], 'starting_round'):
            starting_round = self.reports[sid].starting_round
            self.client_starting_rounds[client_id] = starting_round
            staleness = self.current_round - starting_round
            self.client_staleness[client_id] = staleness
            logging.info(f"从报告中获取客户端 {client_id} 的起始轮次: {starting_round}, 陈旧度: {staleness}")
        else:
            logging.warning(f"客户端 {client_id} 的报告中没有起始轮次信息")

    async def aggregate_deltas(self, updates, deltas_received):
        """Aggregate weight updates from the clients using federated averaging."""
        # Extract the total number of samples
        total_updates = len(updates)

        # Perform weighted averaging
        avg_update = {
            name: self.trainer.zeros(delta.shape)
            for name, delta in deltas_received[0].items()
        }
        
        # Reset the list of losses for this round
        self.current_round_losses = []
        
        # 记录本轮参与聚合的客户端陈旧度
        aggregated_staleness = []
        
        for i, update in enumerate(deltas_received):
            report = updates[i].report
            num_samples = report.num_samples
            client_id = updates[i].client_id
            
            # 检查report中是否有alpha属性，如果没有则使用默认值1.0
            magnified_factor = 1.0
            if hasattr(report, 'alpha'):
                magnified_factor = report.alpha
            
            logging.info("the corresponding magnified ratio is %f" % magnified_factor)
            
            # 更新客户端起始轮次和计算陈旧度
            if hasattr(report, 'starting_round'):
                self.client_starting_rounds[client_id] = report.starting_round
                staleness = self.current_round - report.starting_round
                self.client_staleness[client_id] = staleness
                aggregated_staleness.append(staleness)
                logging.info(f"聚合客户端 {client_id} 的陈旧度为 {staleness}")
            else:
                logging.warning(f"聚合客户端 {client_id} 的报告中没有起始轮次信息")

            # Collect loss information if available
            if hasattr(report, 'final_loss'):
                loss = report.final_loss
                # Store loss for this client
                self.current_round_losses.append(loss)
                logging.info(f"[Server] Client {client_id} reported loss: {loss}")
                
            for name, delta in update.items():
                # Use weighted average by the number of samples
                avg_update[name] += delta * (1 / total_updates)

            # Yield to other tasks in the server
            await asyncio.sleep(0)
        
        # 输出本轮聚合的陈旧度信息
        if aggregated_staleness:
            avg_agg_staleness = sum(aggregated_staleness) / len(aggregated_staleness)
            logging.info(f"本轮聚合的客户端平均陈旧度: {avg_agg_staleness:.4f}")
        
        return avg_update
    
    async def wrap_up(self) -> None:
        """在每轮训练结束时记录结果"""
        await super().wrap_up()
        
        # 记录准确率
        if hasattr(self, 'accuracy'):
            self.accuracy_log.append(self.accuracy)
            for threshold in self.first_reach_time:
                if self.first_reach_time[threshold] is None and self.accuracy >= threshold:
                    self.first_reach_time[threshold] = time.time() - self.start_time
                    logging.info(f"首次达到{threshold*100:.1f}%准确率，用时{self.first_reach_time[threshold]:.2f}秒")
        
        # 计算所有活跃客户端的平均陈旧度
        staleness_values = list(self.client_staleness.values())
        avg_staleness = sum(staleness_values) / len(staleness_values) if staleness_values else 0.0
        self.staleness_history.append(avg_staleness)
        
        # 输出详细的陈旧度信息
        if staleness_values:
            max_staleness = max(staleness_values)
            min_staleness = min(staleness_values)
            logging.info(f"客户端陈旧度统计: 平均={avg_staleness:.4f}, 最大={max_staleness}, 最小={min_staleness}, 客户端数={len(staleness_values)}")
            logging.info(f"客户端陈旧度详情: {self.client_staleness}")
        else:
            logging.warning("没有客户端陈旧度信息")
        
        # 输出本轮结果
        logging.info(f"\n【第{self.current_round}轮结束】\n平均过时程度: {avg_staleness:.4f}\n当前准确率: {self.accuracy:.4f}")
        
        # 记录过去10轮的平均准确率和最佳准确率
        if self.accuracy_log:
            avg_last_10 = sum(self.accuracy_log[-10:]) / min(10, len(self.accuracy_log))
            best_acc = max(self.accuracy_log)
            logging.info(f"过去10轮平均准确率: {avg_last_10:.4f}, 最佳准确率: {best_acc:.4f}")
        
        # 写入CSV文件
        csv_processor.write_csv(
            self.csv_path,
            [self.current_round, time.time() - self.start_time, self.accuracy, avg_staleness]
        )
        logging.info(f"已写入第{self.current_round}轮数据：[round={self.current_round}, elapsed_time={time.time() - self.start_time:.2f}, accuracy={self.accuracy:.4f}, avg_staleness={avg_staleness:.4f}]")
    
    def get_logged_items(self) -> dict:
        """Get items to be logged by the LogProgressCallback class in a .csv file."""
        logged_items = super().get_logged_items()

        # Add the average loss to the logged items
        logged_items["loss"] = self.avg_loss
        
        # 添加陈旧度
        staleness_values = list(self.client_staleness.values())
        avg_staleness = sum(staleness_values) / len(staleness_values) if staleness_values else 0.0
        logged_items["avg_staleness"] = avg_staleness
        
        # 添加最大和最小陈旧度
        if staleness_values:
            logged_items["max_staleness"] = max(staleness_values)
            logged_items["min_staleness"] = min(staleness_values)
        else:
            logged_items["max_staleness"] = 0.0
            logged_items["min_staleness"] = 0.0

        return logged_items
