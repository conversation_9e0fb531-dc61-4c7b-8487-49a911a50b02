# RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)

## 概述

RESCAFL是基于FedAsync改进的异步联邦学习算法，主要特性包括：

1. **服务器端时间估算**：对客户端的计算时间和通信时间进行预测
2. **贪心客户端选择**：基于陈旧度和运行时间进行智能客户端选择
3. **保持Plato架构**：继承Plato的Socket.IO通信机制和组件架构

## 与FedAsync的主要区别

### 🔧 主要修改在服务器端 (`rescafl_server.py`)

#### 新增功能：

1. **时间估算方法**：
   - `estimate_computation_time()` - 估算客户端计算时间
   - `estimate_communication_time()` - 估算客户端通信时间

2. **贪心选择算法**：
   - `calculate_client_priority()` - 计算客户端优先级
   - `greedy_client_selection()` - 贪心客户端选择
   - `choose_clients()` - 重写客户端选择方法

3. **性能跟踪**：
   - `update_client_metrics()` - 更新客户端性能指标
   - 客户端计算能力和带宽记录

#### 修改的方法：
- `__init__()` - 添加RESCAFL特有属性
- `configure()` - 添加RESCAFL参数配置
- `get_logged_items()` - 添加时间估算相关日志
- `aggregate_weights()` - 添加性能指标更新

### 📱 客户端轻微修改 (`rescafl_client.py`)

#### 新增功能：
1. **时间记录**：
   - 计算开始/结束时间记录
   - 通信开始/结束时间记录
   - 轮次总时间记录

2. **能力模拟**：
   - 客户端计算能力因子
   - 客户端通信能力因子

#### 修改的方法：
- `__init__()` - 添加时间记录属性
- `_start_training()` - 添加时间记录
- `_train()` - 添加计算时间记录
- `customize_report()` - 添加时间信息到报告
- `configure()` - 添加异构性配置

### 🧮 算法保持不变 (`rescafl_algorithm.py`)

- **完全继承FedAsync的聚合逻辑**
- 主要改进在服务器端的客户端选择，不在聚合算法

### 🏃 训练器轻微修改 (`rescafl_trainer.py`)

#### 新增功能：
- `get_training_time()` - 获取实际训练时间
- 训练开始/结束时间记录

## 文件结构

```
RESCAFL/
├── rescafl.py                    # 主入口文件
├── rescafl_server.py            # 服务器实现（主要修改）
├── rescafl_client.py            # 客户端实现（轻微修改）
├── rescafl_algorithm.py         # 算法实现（保持FedAsync逻辑）
├── rescafl_trainer.py           # 训练器实现（轻微修改）
├── rescafl_MNIST_lenet5.yml     # 配置文件（添加RESCAFL参数）
├── run_rescafl_mnist.py         # 运行脚本
└── README.md                    # 说明文档
```

## 配置参数

### RESCAFL特有参数

```yaml
server:
    # 贪心选择参数
    staleness_weight: 0.5          # 陈旧度权重
    time_weight: 0.5               # 时间权重
    max_staleness: 10              # 最大允许陈旧度
    
    # 时间估算参数
    base_computation_time: 10.0    # 基础计算时间（秒）
    base_communication_time: 2.0   # 基础通信时间（秒）
    model_size_mb: 5.0             # 模型大小（MB）

clients:
    simulate_heterogeneity: true   # 启用客户端异构性模拟

algorithm:
    staleness_penalty_factor: 0.9  # 陈旧度惩罚因子
    performance_weight_factor: 0.1 # 性能权重因子
    min_weight: 0.1                # 最小权重
    weight_strategy: exponential   # 权重计算策略

parameters:
    unqualified_ratio: 0.1         # 不合格客户端比例
    average_duration: 15           # 平均持续时间
```

## 运行方法

### 方法1：使用运行脚本
```bash
python run_rescafl_mnist.py
```

### 方法2：直接运行
```bash
python rescafl.py -c rescafl_MNIST_lenet5.yml
```

## 核心算法流程

1. **服务器启动** → 初始化时间估算和选择参数
2. **客户端连接** → 记录客户端能力和历史性能
3. **贪心选择** → 基于陈旧度和时间估算选择客户端
4. **客户端训练** → 记录实际计算和通信时间
5. **性能更新** → 更新客户端性能历史记录
6. **模型聚合** → 使用FedAsync的聚合逻辑

## 日志输出

RESCAFL会输出以下额外的日志信息：
- 平均估算计算时间 (`avg_est_comp_time`)
- 平均估算通信时间 (`avg_est_comm_time`)
- 平均估算总时间 (`avg_est_total_time`)
- 平均陈旧度 (`avg_staleness`)
- 最大/最小陈旧度 (`max_staleness`, `min_staleness`)

## 代码注释说明

所有与FedAsync不同的修改都标注了：
```python
# 此处做了修改，具体为：[具体修改内容说明]
```

这样可以清楚地识别RESCAFL相对于FedAsync的所有改进点。
