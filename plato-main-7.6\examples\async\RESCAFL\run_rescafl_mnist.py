#!/usr/bin/env python3
"""
运行RESCAFL算法的脚本

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 服务器端计算和通信时间估算
2. 基于陈旧度和运行时间的贪心客户端选择

使用方法:
python run_rescafl_mnist.py

或者直接运行:
python rescafl.py -c rescafl_MNIST_lenet5.yml
"""

import os
import sys
import subprocess

def main():
    """运行RESCAFL实验"""
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置配置文件路径
    config_file = os.path.join(current_dir, "rescafl_MNIST_lenet5.yml")
    rescafl_script = os.path.join(current_dir, "rescafl.py")
    
    # 检查文件是否存在
    if not os.path.exists(config_file):
        print(f"错误: 配置文件不存在: {config_file}")
        return
    
    if not os.path.exists(rescafl_script):
        print(f"错误: RESCAFL脚本不存在: {rescafl_script}")
        return
    
    # 构建命令
    cmd = [sys.executable, rescafl_script, "-c", config_file]
    
    print("=" * 60)
    print("运行RESCAFL联邦学习实验")
    print("=" * 60)
    print(f"配置文件: {config_file}")
    print(f"运行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 运行命令
        result = subprocess.run(cmd, cwd=current_dir, check=True)
        print("\n实验完成!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n实验运行失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n实验被用户中断")
        return 1
    except Exception as e:
        print(f"\n运行过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
