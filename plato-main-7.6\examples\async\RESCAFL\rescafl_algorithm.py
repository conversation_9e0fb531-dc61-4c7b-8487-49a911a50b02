"""
A federated learning algorithm using RESCAFL.

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 基于陈旧度的权重聚合
2. 考虑客户端性能差异的权重调整
3. 继承Plato的FedAvg算法框架

Reference:
Based on FedAsync:
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).
"""
import logging
from collections import OrderedDict

from plato.algorithms import fedavg


class Algorithm(fedavg.Algorithm):
    """The federated learning algorithm for RESCAFL, used by the server."""

    async def aggregate_weights(
        self, baseline_weights, weights_received, mixing=0.9, **kwargs
    ):
        """Aggregates the weights received into baseline weights."""
        # Actually update the global model's weights (PyTorch-only implementation)
        # 此处做了修改，具体为：保持与FedAsync相同的聚合逻辑，RESCAFL的主要改进在服务器端的客户端选择
        # logging.info("new aggregation method for RESCAFL")
        updated_weights = OrderedDict()
        for name, weight in baseline_weights.items():
            updated_weights[name] = (
                weight * (1 - mixing) + weights_received[name] * mixing
            )

        return updated_weights
