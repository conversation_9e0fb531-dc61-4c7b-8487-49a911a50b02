"""
RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning) 算法实现。

主要特性：
1. 基于陈旧度的权重聚合
2. 考虑客户端性能差异的权重调整
3. 继承Plato的FedAvg算法框架
"""

import logging
import math
import statistics
from collections import OrderedDict
from typing import Dict, List, Optional

import torch

from plato.algorithms import fedavg
from plato.config import Config


class Algorithm(fedavg.Algorithm):
    """RESCAFL算法，继承自FedAvg算法"""

    def __init__(self, trainer=None):
        super().__init__(trainer)
        
        # RESCAFL特有参数
        self.staleness_penalty_factor = 0.9  # 陈旧度惩罚因子
        self.performance_weight_factor = 0.1  # 性能权重因子
        self.min_weight = 0.1  # 最小权重
        self.max_staleness = 10  # 最大允许陈旧度
        
        # 权重计算策略
        self.weight_strategy = "exponential"  # exponential, linear, polynomial
        
        logging.info("RESCAFL算法初始化完成")

    def configure(self) -> None:
        """配置RESCAFL算法参数"""
        config = Config()
        
        # 从配置文件读取参数
        if hasattr(config.algorithm, 'staleness_penalty_factor'):
            self.staleness_penalty_factor = config.algorithm.staleness_penalty_factor
        if hasattr(config.algorithm, 'performance_weight_factor'):
            self.performance_weight_factor = config.algorithm.performance_weight_factor
        if hasattr(config.algorithm, 'min_weight'):
            self.min_weight = config.algorithm.min_weight
        if hasattr(config.algorithm, 'max_staleness'):
            self.max_staleness = config.algorithm.max_staleness
        if hasattr(config.algorithm, 'weight_strategy'):
            self.weight_strategy = config.algorithm.weight_strategy

        logging.info(f"RESCAFL算法参数: staleness_penalty={self.staleness_penalty_factor}, "
                    f"performance_weight={self.performance_weight_factor}, "
                    f"weight_strategy={self.weight_strategy}")

    def calculate_staleness_weight(self, staleness: int) -> float:
        """根据陈旧度计算权重"""
        if staleness > self.max_staleness:
            return 0.0  # 超过最大陈旧度的客户端权重为0
        
        if self.weight_strategy == "exponential":
            # 指数衰减权重
            weight = math.exp(-self.staleness_penalty_factor * staleness)
        elif self.weight_strategy == "linear":
            # 线性衰减权重
            weight = max(0, 1 - self.staleness_penalty_factor * staleness / self.max_staleness)
        elif self.weight_strategy == "polynomial":
            # 多项式衰减权重
            weight = (1 - staleness / self.max_staleness) ** 2
        else:
            # 默认使用指数衰减
            weight = math.exp(-self.staleness_penalty_factor * staleness)
        
        return max(self.min_weight, weight)

    def calculate_performance_weight(self, computation_time: float, communication_time: float,
                                   avg_computation_time: float, avg_communication_time: float) -> float:
        """根据性能计算权重"""
        if avg_computation_time <= 0 or avg_communication_time <= 0:
            return 1.0
        
        # 计算相对性能（时间越短，性能越好）
        comp_performance = avg_computation_time / max(computation_time, 0.1)
        comm_performance = avg_communication_time / max(communication_time, 0.1)
        
        # 综合性能权重
        performance_weight = (comp_performance + comm_performance) / 2
        
        # 限制权重范围
        performance_weight = max(0.5, min(2.0, performance_weight))
        
        return performance_weight

    async def aggregate_weights(self, baseline_weights, weights_received, **kwargs):
        """RESCAFL权重聚合"""
        # 获取额外参数
        staleness = kwargs.get('staleness', 0)
        mixing = kwargs.get('mixing', 1.0)
        
        # 计算陈旧度权重
        staleness_weight = self.calculate_staleness_weight(staleness)
        
        # 调整混合参数
        adjusted_mixing = mixing * staleness_weight
        
        logging.debug(f"聚合权重: staleness={staleness}, staleness_weight={staleness_weight:.4f}, "
                     f"original_mixing={mixing:.4f}, adjusted_mixing={adjusted_mixing:.4f}")
        
        # 调用父类方法进行聚合
        return await super().aggregate_weights(baseline_weights, weights_received, mixing=adjusted_mixing)

    def aggregate_deltas(self, baseline_weights: OrderedDict, deltas_received: List[OrderedDict],
                        client_weights: Optional[Dict[int, float]] = None,
                        staleness_info: Optional[Dict[int, int]] = None,
                        performance_info: Optional[Dict[int, Dict]] = None) -> OrderedDict:
        """聚合客户端增量，考虑陈旧度和性能"""
        
        if not deltas_received:
            return baseline_weights
        
        # 计算每个客户端的综合权重
        total_weight = 0.0
        client_final_weights = {}
        
        for i, delta in enumerate(deltas_received):
            client_id = i  # 假设客户端ID就是索引
            
            # 基础权重（样本数量权重）
            base_weight = client_weights.get(client_id, 1.0) if client_weights else 1.0
            
            # 陈旧度权重
            staleness = staleness_info.get(client_id, 0) if staleness_info else 0
            staleness_weight = self.calculate_staleness_weight(staleness)
            
            # 性能权重
            performance_weight = 1.0
            if performance_info and client_id in performance_info:
                perf_info = performance_info[client_id]
                comp_time = perf_info.get('computation_time', 1.0)
                comm_time = perf_info.get('communication_time', 1.0)
                avg_comp_time = perf_info.get('avg_computation_time', comp_time)
                avg_comm_time = perf_info.get('avg_communication_time', comm_time)
                
                performance_weight = self.calculate_performance_weight(
                    comp_time, comm_time, avg_comp_time, avg_comm_time
                )
            
            # 综合权重
            final_weight = base_weight * staleness_weight * (1 + self.performance_weight_factor * (performance_weight - 1))
            client_final_weights[client_id] = final_weight
            total_weight += final_weight
            
            logging.debug(f"客户端 {client_id}: base_weight={base_weight:.4f}, "
                         f"staleness_weight={staleness_weight:.4f}, "
                         f"performance_weight={performance_weight:.4f}, "
                         f"final_weight={final_weight:.4f}")
        
        # 归一化权重
        if total_weight > 0:
            for client_id in client_final_weights:
                client_final_weights[client_id] /= total_weight
        
        # 执行加权聚合
        aggregated_weights = OrderedDict()
        
        for name, param in baseline_weights.items():
            if isinstance(param, torch.Tensor):
                aggregated_delta = torch.zeros_like(param)
                
                for i, delta in enumerate(deltas_received):
                    if name in delta:
                        client_id = i
                        weight = client_final_weights.get(client_id, 0.0)
                        aggregated_delta += weight * delta[name]
                
                aggregated_weights[name] = param + aggregated_delta
            else:
                aggregated_weights[name] = param
        
        logging.info(f"RESCAFL聚合完成，使用了 {len(deltas_received)} 个客户端的更新")
        
        return aggregated_weights

    def compute_weight_deltas(self, baseline_weights: OrderedDict, 
                            weights_received: List[OrderedDict]) -> List[OrderedDict]:
        """计算权重增量（重写父类方法）"""
        deltas = []
        
        for weights in weights_received:
            delta = OrderedDict()
            for name, param in weights.items():
                if name in baseline_weights:
                    if isinstance(param, torch.Tensor) and isinstance(baseline_weights[name], torch.Tensor):
                        delta[name] = param - baseline_weights[name]
                    else:
                        delta[name] = param
                else:
                    delta[name] = param
            deltas.append(delta)
        
        return deltas

    def get_staleness_statistics(self, staleness_list: List[int]) -> Dict[str, float]:
        """获取陈旧度统计信息"""
        if not staleness_list:
            return {"avg": 0, "min": 0, "max": 0, "std": 0}
        
        return {
            "avg": statistics.mean(staleness_list),
            "min": min(staleness_list),
            "max": max(staleness_list),
            "std": statistics.stdev(staleness_list) if len(staleness_list) > 1 else 0
        }

    def get_weight_statistics(self, weights_dict: Dict[int, float]) -> Dict[str, float]:
        """获取权重统计信息"""
        if not weights_dict:
            return {"avg": 0, "min": 0, "max": 0, "std": 0}
        
        weights = list(weights_dict.values())
        return {
            "avg": statistics.mean(weights),
            "min": min(weights),
            "max": max(weights),
            "std": statistics.stdev(weights) if len(weights) > 1 else 0
        }
