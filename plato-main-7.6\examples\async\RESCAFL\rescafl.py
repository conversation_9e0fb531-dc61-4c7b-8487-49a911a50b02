"""
A federated learning training session using RESCAFL.

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 服务器端计算和通信时间估算
2. 基于陈旧度和运行时间的贪心客户端选择

Reference:
Based on FedAsync:
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).
"""

import rescafl_client
import rescafl_algorithm
import rescafl_server
import rescafl_trainer


def main():
    """A Plato federated learning training session using RESCAFL."""
    # 此处做了修改，具体为：将fedasync改为rescafl相关组件
    algorithm = rescafl_algorithm.Algorithm
    trainer = rescafl_trainer.Trainer
    client = rescafl_client.Client(algorithm=algorithm, trainer=trainer)
    server = rescafl_server.Server(algorithm=algorithm, trainer=trainer)
    server.run(client)

if __name__ == "__main__":
    main()
