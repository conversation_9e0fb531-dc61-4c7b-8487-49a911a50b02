"""
RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning) 客户端实现。

主要特性：
1. 时间记录和跟踪
2. 陈旧度感知
3. 继承Plato的Socket.IO通信机制
"""

import copy
import logging
import os
import time
import random
import numpy as np
from types import SimpleNamespace

from plato.clients import simple
from plato.config import Config
from plato.utils import fonts


class Client(simple.Client):
    """RESCAFL联邦学习客户端，继承自Simple客户端"""

    def __init__(
        self,
        model=None,
        datasource=None,
        algorithm=None,
        trainer=None,
        callbacks=None,
        trainer_callbacks=None,
    ):
        super().__init__(
            model, datasource, algorithm, trainer, callbacks, trainer_callbacks
        )
        
        # RESCAFL特有属性
        self.computation_start_time = None
        self.computation_end_time = None
        self.communication_start_time = None
        self.communication_end_time = None
        self.round_start_time = None
        self.round_end_time = None
        
        # 客户端能力模拟
        self.computation_capability = random.uniform(0.5, 2.0)  # 计算能力因子
        self.communication_capability = random.uniform(0.5, 3.0)  # 通信能力因子
        
        # 性能历史记录
        self.computation_times = []
        self.communication_times = []
        self.round_times = []
        
        logging.info(f"RESCAFL客户端 #{self.client_id} 初始化完成，"
                    f"计算能力因子: {self.computation_capability:.2f}, "
                    f"通信能力因子: {self.communication_capability:.2f}")

    def configure(self) -> None:
        """配置RESCAFL客户端"""
        super().configure()
        
        config = Config()
        
        # 模拟客户端异构性
        if hasattr(config.clients, 'simulate_heterogeneity') and config.clients.simulate_heterogeneity:
            # 根据客户端ID设置不同的能力
            random.seed(self.client_id)  # 确保每个客户端的能力是确定的
            self.computation_capability = random.uniform(0.3, 2.5)
            self.communication_capability = random.uniform(0.2, 4.0)
            
            logging.info(f"客户端 #{self.client_id} 异构性配置: "
                        f"计算能力={self.computation_capability:.2f}, "
                        f"通信能力={self.communication_capability:.2f}")

    async def _start_training(self, inbound_payload):
        """开始训练（重写父类方法以添加时间记录）"""
        self.round_start_time = time.time()
        self.communication_start_time = time.time()
        
        # 调用父类方法
        result = await super()._start_training(inbound_payload)
        
        self.communication_end_time = time.time()
        self.round_end_time = time.time()
        
        # 记录时间
        self._record_times()
        
        return result

    async def _train(self, server_model=None):
        """训练模型（重写父类方法以添加时间记录）"""
        # 提取初始全局模型
        init_global_model = copy.deepcopy(self.algorithm.extract_weights())
        
        logging.info(
            fonts.colourize(
                f"[{self}] 开始第 {self.current_round} 轮训练"
            )
        )

        # 测试全局模型在本地数据集上的性能
        global_accuracy = 0
        if (hasattr(Config().clients, "do_global_test") and Config().clients.do_global_test):
            global_accuracy = self.trainer.test(self.testset, self.testset_sampler)
            logging.info("[%s] 全局模型测试准确率: %.2f%%", self, 100 * global_accuracy)

            if global_accuracy == -1:
                logging.info(
                    fonts.colourize(
                        f"[{self}] 全局模型测试失败，断开连接"
                    )
                )
                await self.sio.disconnect()

        # 开始本地训练
        self.computation_start_time = time.time()
        
        try:
            if hasattr(self.trainer, "current_round"):
                self.trainer.current_round = self.current_round
            
            # 模拟计算能力差异
            training_time = self.trainer.train(self.trainset, self.sampler, server_model)
            
            # 根据客户端能力调整实际训练时间
            if hasattr(Config().clients, 'simulate_heterogeneity') and Config().clients.simulate_heterogeneity:
                actual_training_time = training_time * self.computation_capability
                if actual_training_time != training_time:
                    # 模拟额外的计算时间
                    import asyncio
                    await asyncio.sleep(max(0, actual_training_time - training_time))
                    training_time = actual_training_time

        except ValueError as exc:
            logging.info(
                fonts.colourize(f"[{self}] 训练过程中出错: {exc}")
            )
            await self.sio.disconnect()

        self.computation_end_time = time.time()

        # 提取模型权重和增量
        weights = self.algorithm.extract_weights()
        deltas = self.algorithm.compute_weight_deltas(init_global_model, [weights])[0]

        # 生成训练报告
        accuracy = 0
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval")
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                logging.info(
                    fonts.colourize(
                        f"[{self}] 测试失败，断开连接"
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"):
                logging.info("[%s] 测试困惑度: %.2f", self, accuracy)
            else:
                logging.info("[%s] 测试准确率: %.2f%%", self, 100 * accuracy)

        # 计算样本数量
        if self.sampler is None:
            num_samples = self.datasource.num_train_examples()
        else:
            num_samples = self.sampler.num_samples()

        # 创建报告
        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=num_samples,
            accuracy=accuracy,
            global_accuracy=global_accuracy,
            training_time=training_time,
            comm_time=time.time(),
            update_response=False,
            deltas=deltas,
        )

        # 添加RESCAFL特有的报告信息
        report = self.customize_report(report)

        return report, weights

    def customize_report(self, report: SimpleNamespace) -> SimpleNamespace:
        """自定义报告，添加RESCAFL特有信息"""
        # 计算各阶段时间
        if self.computation_start_time and self.computation_end_time:
            report.actual_computation_time = self.computation_end_time - self.computation_start_time
        else:
            report.actual_computation_time = 0

        if self.communication_start_time and self.communication_end_time:
            report.actual_communication_time = self.communication_end_time - self.communication_start_time
        else:
            report.actual_communication_time = 0

        if self.round_start_time and self.round_end_time:
            report.actual_round_time = self.round_end_time - self.round_start_time
        else:
            report.actual_round_time = 0

        # 添加客户端能力信息
        report.computation_capability = self.computation_capability
        report.communication_capability = self.communication_capability

        # 添加历史性能信息
        if self.computation_times:
            report.avg_computation_time = sum(self.computation_times) / len(self.computation_times)
        else:
            report.avg_computation_time = 0

        if self.communication_times:
            report.avg_communication_time = sum(self.communication_times) / len(self.communication_times)
        else:
            report.avg_communication_time = 0

        logging.info(f"[{self}] 报告生成: 计算时间={report.actual_computation_time:.2f}s, "
                    f"通信时间={report.actual_communication_time:.2f}s, "
                    f"总时间={report.actual_round_time:.2f}s")

        return report

    def _record_times(self):
        """记录时间性能"""
        if self.computation_start_time and self.computation_end_time:
            comp_time = self.computation_end_time - self.computation_start_time
            self.computation_times.append(comp_time)
            
            # 保持最近10次记录
            if len(self.computation_times) > 10:
                self.computation_times = self.computation_times[-10:]

        if self.communication_start_time and self.communication_end_time:
            comm_time = self.communication_end_time - self.communication_start_time
            self.communication_times.append(comm_time)
            
            # 保持最近10次记录
            if len(self.communication_times) > 10:
                self.communication_times = self.communication_times[-10:]

        if self.round_start_time and self.round_end_time:
            round_time = self.round_end_time - self.round_start_time
            self.round_times.append(round_time)
            
            # 保持最近10次记录
            if len(self.round_times) > 10:
                self.round_times = self.round_times[-10:]

    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        stats = {}
        
        if self.computation_times:
            stats['avg_computation_time'] = sum(self.computation_times) / len(self.computation_times)
            stats['min_computation_time'] = min(self.computation_times)
            stats['max_computation_time'] = max(self.computation_times)
        
        if self.communication_times:
            stats['avg_communication_time'] = sum(self.communication_times) / len(self.communication_times)
            stats['min_communication_time'] = min(self.communication_times)
            stats['max_communication_time'] = max(self.communication_times)
        
        if self.round_times:
            stats['avg_round_time'] = sum(self.round_times) / len(self.round_times)
            stats['min_round_time'] = min(self.round_times)
            stats['max_round_time'] = max(self.round_times)
        
        stats['computation_capability'] = self.computation_capability
        stats['communication_capability'] = self.communication_capability
        
        return stats
