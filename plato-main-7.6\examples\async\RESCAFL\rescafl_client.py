"""
A federated learning client using RESCAFL.

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 时间记录和跟踪
2. 陈旧度感知
3. 继承Plato的Socket.IO通信机制

Reference:
Based on FedAsync:
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).
"""
import copy
import logging
import os
import time
import random
import numpy as np

from types import SimpleNamespace
from plato.clients import simple
from plato.config import Config
from plato.utils import fonts


# pass global model to trainer to act as a regularizer
class Client(simple.Client):
    """A federated learning client using the RESCAFL algorithm."""
    
    def __init__(
            self,
            model=None,
            datasource=None,
            algorithm=None,
            trainer=None,
            callbacks=None,
            trainer_callbacks=None,
    ):
        super().__init__(model, datasource, algorithm, trainer, callbacks, trainer_callbacks)
        self.quality = None  # Whether the client is qualified to complete the training and communication
        self.communication_time = 0
        self.training_time = 0
        self.sojourn_time = 0
        self.para_file = None
        
        # 此处做了修改，具体为：添加RESCAFL特有的时间记录属性
        self.computation_start_time = None
        self.computation_end_time = None
        self.communication_start_time = None
        self.communication_end_time = None
        self.round_start_time = None
        self.round_end_time = None
        
        # 此处做了修改，具体为：添加客户端能力模拟
        self.computation_capability = random.uniform(0.5, 2.0)  # 计算能力因子
        self.communication_capability = random.uniform(0.5, 3.0)  # 通信能力因子
        
        # 此处做了修改，具体为：添加性能历史记录
        self.computation_times = []
        self.communication_times = []
        self.round_times = []

    def create_magnified_file(self):
        """当配置了max_concurrency时,会创建另一个线程的trainer，导致无法存储更改，因此创建创建文件存储"""
        # 在当前目录创建文件，文件名为self.client_id_magnified.csv
        filename = f"{self.client_id}_magnified.csv"
        with open(filename, 'w') as f:
            f.write("alpha,beta,process_id\n")
        # 保存文件名
        return filename

    async def _start_training(self, inbound_payload):
        """Complete one round of training on this client. 所以plato/clients中的用户只需要复写_train函数即可完成不同损失函数的模型训练"""
        # 此处做了修改，具体为：添加轮次开始时间记录
        self.round_start_time = time.time()
        self.communication_start_time = time.time()
        
        # 开始训练的第一步就是把全局模型加载到本地用户上
        self._load_payload(inbound_payload)
        # 传入server model
        report, outbound_payload = await self._train(inbound_payload)

        if Config().is_edge_server():
            logging.info(
                "[Server #%d] Model aggregated on edge server (%s).", os.getpid(), self
            )
        else:
            logging.info("[%s] Model trained.", self)

        # 此处做了修改，具体为：添加轮次结束时间记录
        self.communication_end_time = time.time()
        self.round_end_time = time.time()
        
        # 记录时间
        self._record_times()

        return report, outbound_payload

    async def _train(self, server_model=None):
        """接收server model"""
        # 提取初始加载的全局模型
        init_global_model = copy.deepcopy(self.algorithm.extract_weights())
        logging.info(
            fonts.colourize(
                f"[{self}] Started training in communication round #{self.current_round}."
            )
        )

        # test global model on local dataset
        if (hasattr(Config().clients, "do_global_test") and Config().clients.do_global_test):
            global_accuracy = self.trainer.test(self.testset, self.testset_sampler)
            logging.info("[%s] Test global accuracy: %.2f%%", self, 100 * global_accuracy)

            if global_accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Global accuracy on local data is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()
        else:
            global_accuracy = 0

        # 此处做了修改，具体为：添加计算开始时间记录
        self.computation_start_time = time.time()

        # Perform model training
        try:
            if hasattr(self.trainer, "current_round"):
                self.trainer.current_round = self.current_round
            training_time = self.trainer.train(self.trainset, self.sampler, server_model)

        except ValueError as exc:
            logging.info(
                fonts.colourize(f"[{self}] Error occurred during training: {exc}")
            )
            await self.sio.disconnect()

        # 此处做了修改，具体为：添加计算结束时间记录
        self.computation_end_time = time.time()

        # Extract model weights and biases
        weights = self.algorithm.extract_weights()
        deltas = self.algorithm.compute_weight_deltas(init_global_model, [weights])[0]

        # Generate a report for the server, performing model testing if applicable
        if (hasattr(Config().clients, "do_test") and Config().clients.do_test) and (
            not hasattr(Config().clients, "test_interval")
            or self.current_round % Config().clients.test_interval == 0
        ):
            accuracy = self.trainer.test(self.testset, self.testset_sampler)

            if accuracy == -1:
                # The testing process failed, disconnect from the server
                logging.info(
                    fonts.colourize(
                        f"[{self}] Accuracy is -1 when testing. Disconnecting from the server."
                    )
                )
                await self.sio.disconnect()

            if hasattr(Config().trainer, "target_perplexity"):
                logging.info("[%s] Test perplexity: %.2f", self, accuracy)
            else:
                logging.info("[%s] Test accuracy: %.2f%%", self, 100 * accuracy)
        else:
            accuracy = 0

        comm_time = time.time()

        if (
            hasattr(Config().clients, "sleep_simulation")
            and Config().clients.sleep_simulation
        ):
            sleep_seconds = Config().client_sleep_times[self.client_id - 1]
            avg_training_time = Config().clients.avg_training_time

            training_time = (
                avg_training_time + sleep_seconds
            ) * Config().trainer.epochs
        if self.sampler is None:
            num_samples = self.datasource.num_train_examples()
        else:
            num_samples = self.sampler.num_samples()
        report = SimpleNamespace(
            client_id=self.client_id,
            num_samples=num_samples,
            accuracy=accuracy,
            global_accuracy=global_accuracy,
            training_time=training_time,
            comm_time=comm_time,
            update_response=False,
            deltas=deltas,
        )

        self._report = self.customize_report(report)

        return self._report, weights

    def configure(self) -> None:
        """Prepares this client for training."""
        super().configure()
        logging.info("Configuration started")
        # num_train = self.sampler.num_samples()
        # model_size = self.get_model_size()
        # 根据config中的average_duration为基准，根据正态分布随机生成一个sojourn_time
        unqualified_ratio = 0
        average_duration = 0
        stand_devation = 3

        # 判断是否有max_concurrency参数，如果有则创建文件
        if hasattr(Config().trainer, "max_concurrency"):
            self.para_file = self.create_magnified_file()

        if hasattr(Config().parameters, "unqualified_ratio"):
            unqualified_ratio = Config().parameters.unqualified_ratio
        if hasattr(Config().parameters, "average_duration"):
            average_duration = Config().parameters.average_duration
        self.sojourn_time = random.normalvariate(average_duration, stand_devation)
        sojourn_time = self.sojourn_time

        # 根据config中的unqualified_ratio为0到1间的概率，将self.sojourn_time方法随机生成一个0.5倍到1.5倍的值
        if np.random.uniform(0, 1) < unqualified_ratio:
            sojourn_time = self.sojourn_time * random.uniform(1, 1.2)
            self.quality = False
        else:
            self.quality = True
        self.trainer.set_client_quality(self.quality)
        # 在0,1之间根据高斯分布采样，根据model_size得到一个0到1之间的值，作为communication_time的比例
        communication_rate = random.normalvariate(0.5, 0.15)
        training_rate = random.normalvariate(0.5, 0.1)
        total_rate = communication_rate + training_rate
        communication_rate = communication_rate / total_rate
        training_rate = training_rate / total_rate
        self.communication_time = sojourn_time * communication_rate
        self.training_time = sojourn_time * training_rate
        logging.info("sojourn_time: %f, communication_time: %f, training_time: %f" % (
            self.sojourn_time, self.communication_time, self.training_time))
        logging.info("Configuration ended")

        # 此处做了修改，具体为：添加客户端异构性模拟配置
        config = Config()
        if hasattr(config.clients, 'simulate_heterogeneity') and config.clients.simulate_heterogeneity:
            # 根据客户端ID设置不同的能力
            random.seed(self.client_id)  # 确保每个客户端的能力是确定的
            self.computation_capability = random.uniform(0.3, 2.5)
            self.communication_capability = random.uniform(0.2, 4.0)
            
            logging.info(f"客户端 #{self.client_id} 异构性配置: "
                        f"计算能力={self.computation_capability:.2f}, "
                        f"通信能力={self.communication_capability:.2f}")

    def customize_report(self, report: SimpleNamespace) -> SimpleNamespace:
        """Wrap up generating the report with any additional information."""
        if hasattr(Config().trainer, "max_concurrency"):
            #读文件，获取alpha,beta,process_id
            with open(self.para_file, 'r') as f:
                lines = f.readlines()
                last_line = lines[-1]
                #判断是否有记录，若无则跳过
                if last_line == "alpha,beta,process_id\n":
                    return report
                alpha, beta, process_id = last_line.split(',')
                alpha = float(alpha)
                beta = float(beta)
                process_id = int(process_id)
                self.trainer.set_alpha(alpha)
                self.trainer.set_epoch_rate(beta)
        report.quality = self.quality
        report.epoch_rate = self.trainer.get_epoch_rate()
        report.alpha = self.trainer.get_alpha()

        # Add loss information to the report
        if hasattr(self.trainer, 'get_final_loss'):
            report.final_loss = self.trainer.get_final_loss()
            logging.info(f"Client #{self.client_id} final loss: {report.final_loss}")

        # 此处做了修改，具体为：添加RESCAFL特有的时间记录到报告中
        if self.computation_start_time and self.computation_end_time:
            report.actual_computation_time = self.computation_end_time - self.computation_start_time
        else:
            report.actual_computation_time = 0

        if self.communication_start_time and self.communication_end_time:
            report.actual_communication_time = self.communication_end_time - self.communication_start_time
        else:
            report.actual_communication_time = 0

        if self.round_start_time and self.round_end_time:
            report.actual_round_time = self.round_end_time - self.round_start_time
        else:
            report.actual_round_time = 0

        # 添加客户端能力信息
        report.computation_capability = self.computation_capability
        report.communication_capability = self.communication_capability

        #记录训练数据后将对应参数回归初始化设置
        self.trainer.set_epoch_rate(1)
        self.trainer.set_alpha(1)
        logging.info("quality: %s, epoch_rate: %f, alpha: %f" % (report.quality, report.epoch_rate, report.alpha))
        return report

    # 此处做了修改，具体为：新增时间记录方法
    def _record_times(self):
        """记录时间性能"""
        if self.computation_start_time and self.computation_end_time:
            comp_time = self.computation_end_time - self.computation_start_time
            self.computation_times.append(comp_time)
            
            # 保持最近10次记录
            if len(self.computation_times) > 10:
                self.computation_times = self.computation_times[-10:]

        if self.communication_start_time and self.communication_end_time:
            comm_time = self.communication_end_time - self.communication_start_time
            self.communication_times.append(comm_time)
            
            # 保持最近10次记录
            if len(self.communication_times) > 10:
                self.communication_times = self.communication_times[-10:]

        if self.round_start_time and self.round_end_time:
            round_time = self.round_end_time - self.round_start_time
            self.round_times.append(round_time)
            
            # 保持最近10次记录
            if len(self.round_times) > 10:
                self.round_times = self.round_times[-10:]
