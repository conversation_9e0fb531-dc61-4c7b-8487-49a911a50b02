"""
A federated learning trainer using RESCAFL.

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 保持与FedAsync相同的训练逻辑
2. 添加时间记录功能

Reference:
Based on FedAsync:
<PERSON><PERSON>, C<PERSON>, <PERSON>, S<PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).
"""
import copy
import logging
import torch
import math
import os
import time
import numpy as np

from plato.models import registry as models_registry
from plato.config import Config
from plato.trainers import basic

class Trainer(basic.Trainer):
    """A federated learning trainer using the RESCAFL algorithm."""
    
    def __init__(self, model=None, callbacks=None):
        super().__init__(model, callbacks)
        self.global_model = copy.deepcopy(self.model)
        
        self.client_quality = None
        self.epoch_rate = 1
        self.alpha = 1
        self.epoch_losses = []
        self.final_loss = 0.0
        
        # 此处做了修改，具体为：添加RESCAFL特有的时间记录属性
        self.training_start_time = None
        self.training_end_time = None
        
    def set_client_quality(self, quality):
        self.client_quality = quality

    def set_epoch_rate(self, epoch_rate):
        self.epoch_rate = epoch_rate

    def get_epoch_rate(self):
        return self.epoch_rate

    def set_alpha(self, alpha):
        self.alpha = alpha

    def get_alpha(self):
        return self.alpha

    def train(self, trainset, sampler, servermodel=None, **kwargs) -> float:
        """训练模型，添加时间记录"""
        # 此处做了修改，具体为：添加训练开始时间记录
        self.training_start_time = time.time()
        
        # 加载全局模型
        self.global_model.load_state_dict(servermodel, strict=True)
        
        # 调用父类训练方法
        training_time = super().train(trainset, sampler)
        
        # 此处做了修改，具体为：添加训练结束时间记录
        self.training_end_time = time.time()
        
        return training_time

    def perform_forward_and_backward_passes(self, config, examples, labels):
        init_global_weights = _flatten_weights_from_model(self.global_model, self.device)
        current_weights = _flatten_weights_from_model(self.model, self.device)
        parameter_mu = (
            Config().clients.proximal_term_penalty_constant
            if hasattr(Config().clients, "proximal_term_penalty_constant")
            else 1
        )

        self.optimizer.zero_grad()
        outputs = self.model(examples)

        loss = self._loss_criterion(outputs, labels)

        # 添加近端项
        proximal_term = 0.0
        for param, global_param in zip(self.model.parameters(), self.global_model.parameters()):
            proximal_term += (param - global_param).norm(2)

        loss += (parameter_mu / 2) * proximal_term

        loss.backward()

        # 记录损失
        self.epoch_losses.append(loss.item())

        self.optimizer.step()

        return loss.item()

    def get_final_loss(self):
        """获取最终损失"""
        if self.epoch_losses:
            self.final_loss = self.epoch_losses[-1]
        return self.final_loss

    def get_training_time(self):
        """获取实际训练时间"""
        if self.training_start_time and self.training_end_time:
            return self.training_end_time - self.training_start_time
        return 0.0


def _flatten_weights_from_model(model, device):
    """Flatten weights from a PyTorch model."""
    weights = []
    for param in model.parameters():
        weights.append(param.data.view(-1))
    return torch.cat(weights).to(device)
