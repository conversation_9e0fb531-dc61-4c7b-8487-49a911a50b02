"""
A federated learning server using RESCAFL.

RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning)
基于FedAsync改进，主要特性：
1. 服务器端计算和通信时间估算
2. 基于陈旧度和运行时间的贪心客户端选择

Reference:
Based on FedAsync:
<PERSON><PERSON>, C<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. "Asynchronous federated optimization,"
in Proc. 12th Annual Workshop on Optimization for Machine Learning (OPT 2020).

https://opt-ml.org/papers/2020/paper_28.pdf
"""
import logging
from collections import OrderedDict
import asyncio
import statistics
import random
import time
import math

from plato.config import Config
from plato.servers import fedavg
from plato.utils import fonts


class Server(fedavg.Server):
    """A federated learning server using the RESCAFL algorithm."""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # The hyperparameter of FedAsync with a range of (0, 1)
        self.mixing_hyperparam = 1
        # Whether adjust mixing hyperparameter after each round
        self.adaptive_mixing = False
        self.current_round_losses = []
        self.avg_loss = 0.0
        
        # 此处做了修改，具体为：添加RESCAFL特有的时间估算和客户端选择相关属性
        self.client_computation_times = {}  # 客户端计算时间记录
        self.client_communication_times = {}  # 客户端通信时间记录
        self.client_staleness = {}  # 客户端陈旧度记录
        self.client_last_update_round = {}  # 客户端最后更新轮次
        self.client_capabilities = {}  # 客户端计算能力
        self.client_bandwidth = {}  # 客户端带宽
        
        # 此处做了修改，具体为：添加RESCAFL时间估算参数
        self.base_computation_time = 10.0  # 基础计算时间（秒）
        self.base_communication_time = 2.0  # 基础通信时间（秒）
        self.model_size_mb = 5.0  # 模型大小（MB）
        
        # 此处做了修改，具体为：添加RESCAFL贪心选择参数
        self.staleness_weight = 0.5  # 陈旧度权重
        self.time_weight = 0.5  # 时间权重
        self.max_staleness = 10  # 最大允许陈旧度
        
        logging.info("RESCAFL服务器初始化完成")
        
    def get_logged_items(self) -> dict:
        """Get items to be logged by the LogProgressCallback class in a .csv file."""
        logged_items = super().get_logged_items()

        # 确保基本字段存在
        if "round" not in logged_items:
            logged_items["round"] = self.current_round
        if "elapsed_time" not in logged_items:
            logged_items["elapsed_time"] = self.wall_time - self.initial_wall_time
        if "accuracy" not in logged_items:
            logged_items["accuracy"] = getattr(self, 'accuracy', 0.0)

        # Add the average loss to the logged items
        logged_items["loss"] = self.avg_loss

        # 添加陈旧度相关字段
        if hasattr(self, 'updates') and self.updates:
            staleness_values = []
            for update in self.updates:
                if hasattr(update, 'staleness'):
                    staleness_values.append(update.staleness)
                else:
                    staleness_values.append(0)

            if staleness_values:
                logged_items["avg_staleness"] = sum(staleness_values) / len(staleness_values)
                logged_items["max_staleness"] = max(staleness_values)
                logged_items["min_staleness"] = min(staleness_values)
            else:
                logged_items["avg_staleness"] = 0.0
                logged_items["max_staleness"] = 0.0
                logged_items["min_staleness"] = 0.0
        else:
            logged_items["avg_staleness"] = 0.0
            logged_items["max_staleness"] = 0.0
            logged_items["min_staleness"] = 0.0

        # 此处做了修改，具体为：添加RESCAFL特有的时间估算相关日志字段
        if hasattr(self, 'updates') and self.updates:
            comp_times = [self.estimate_computation_time(update.client_id) for update in self.updates]
            comm_times = [self.estimate_communication_time(update.client_id) for update in self.updates]
            logged_items["avg_est_comp_time"] = statistics.mean(comp_times) if comp_times else 0
            logged_items["avg_est_comm_time"] = statistics.mean(comm_times) if comm_times else 0
            logged_items["avg_est_total_time"] = statistics.mean([c+t for c,t in zip(comp_times, comm_times)]) if comp_times else 0
        else:
            logged_items["avg_est_comp_time"] = 0
            logged_items["avg_est_comm_time"] = 0
            logged_items["avg_est_total_time"] = 0

        # 网络相关字段
        logged_items["network_latency"] = 0.0
        logged_items["network_bandwidth"] = 0.0
        logged_items["network_reliability"] = 1.0
        logged_items["network_success_rate"] = 1.0

        return logged_items
    
    def configure(self) -> None:
        """Configure the mixing hyperparameter for the server, as well as
        other parameters from the configuration file.
        """
        super().configure()

        # Configuring the mixing hyperparameter for FedAsync
        self.adaptive_mixing = (
            hasattr(Config().server, "adaptive_mixing")
            and Config().server.adaptive_mixing
        )

        if not hasattr(Config().server, "mixing_hyperparameter"):
            logging.warning(
                "FedAsync: Variable mixing hyperparameter is required for the FedAsync server."
            )
        else:
            self.mixing_hyperparam = Config().server.mixing_hyperparameter

            if 0 < self.mixing_hyperparam < 1:
                logging.info(
                    "FedAsync: Mixing hyperparameter is set to %s.",
                    self.mixing_hyperparam,
                )
            else:
                logging.warning(
                    "FedAsync: Invalid mixing hyperparameter. "
                    "The hyperparameter needs to be between 0 and 1 (exclusive)."
                )

        # 此处做了修改，具体为：添加RESCAFL特有参数的配置读取
        config = Config()
        if hasattr(config.server, 'staleness_weight'):
            self.staleness_weight = config.server.staleness_weight
        if hasattr(config.server, 'time_weight'):
            self.time_weight = config.server.time_weight
        if hasattr(config.server, 'max_staleness'):
            self.max_staleness = config.server.max_staleness
        if hasattr(config.server, 'base_computation_time'):
            self.base_computation_time = config.server.base_computation_time
        if hasattr(config.server, 'base_communication_time'):
            self.base_communication_time = config.server.base_communication_time
        if hasattr(config.server, 'model_size_mb'):
            self.model_size_mb = config.server.model_size_mb

        logging.info(f"RESCAFL参数配置: staleness_weight={self.staleness_weight}, "
                    f"time_weight={self.time_weight}, max_staleness={self.max_staleness}")

    # 此处做了修改，具体为：新增服务器端计算时间估算方法
    def estimate_computation_time(self, client_id: int) -> float:
        """估算客户端计算时间"""
        # 获取客户端计算能力（如果没有记录，使用随机值模拟）
        if client_id not in self.client_capabilities:
            # 模拟不同客户端的计算能力差异（0.5-2.0倍基础时间）
            self.client_capabilities[client_id] = random.uniform(0.5, 2.0)
        
        capability_factor = self.client_capabilities[client_id]
        
        # 基于历史记录调整估算
        if client_id in self.client_computation_times:
            historical_times = self.client_computation_times[client_id]
            if historical_times:
                avg_historical = statistics.mean(historical_times[-5:])  # 使用最近5次的平均值
                estimated_time = 0.7 * avg_historical + 0.3 * (self.base_computation_time * capability_factor)
            else:
                estimated_time = self.base_computation_time * capability_factor
        else:
            estimated_time = self.base_computation_time * capability_factor
            
        return max(1.0, estimated_time)  # 最小1秒

    # 此处做了修改，具体为：新增服务器端通信时间估算方法
    def estimate_communication_time(self, client_id: int) -> float:
        """估算客户端通信时间"""
        # 获取客户端带宽（如果没有记录，使用随机值模拟）
        if client_id not in self.client_bandwidth:
            # 模拟不同客户端的带宽差异（0.5-3.0倍基础时间）
            self.client_bandwidth[client_id] = random.uniform(0.5, 3.0)
        
        bandwidth_factor = self.client_bandwidth[client_id]
        
        # 基于模型大小和带宽估算通信时间
        estimated_time = (self.model_size_mb * 2) / bandwidth_factor  # 上传+下载
        
        # 基于历史记录调整估算
        if client_id in self.client_communication_times:
            historical_times = self.client_communication_times[client_id]
            if historical_times:
                avg_historical = statistics.mean(historical_times[-5:])
                estimated_time = 0.6 * avg_historical + 0.4 * estimated_time
                
        return max(0.5, estimated_time)  # 最小0.5秒

    # 此处做了修改，具体为：新增客户端陈旧度计算方法
    def calculate_client_staleness(self, client_id: int) -> int:
        """计算客户端陈旧度"""
        if client_id not in self.client_last_update_round:
            return 0
        
        staleness = self.current_round - self.client_last_update_round[client_id]
        return max(0, staleness)

    # 此处做了修改，具体为：新增客户端优先级计算方法（用于贪心选择）
    def calculate_client_priority(self, client_id: int) -> float:
        """计算客户端优先级（用于贪心选择）"""
        # 计算陈旧度
        staleness = self.calculate_client_staleness(client_id)
        
        # 如果陈旧度超过最大值，优先级为0
        if staleness > self.max_staleness:
            return 0.0
        
        # 估算总运行时间
        comp_time = self.estimate_computation_time(client_id)
        comm_time = self.estimate_communication_time(client_id)
        total_time = comp_time + comm_time
        
        # 计算优先级：陈旧度越高优先级越高，时间越短优先级越高
        staleness_score = staleness / (self.max_staleness + 1)  # 归一化到[0,1)
        time_score = 1.0 / (1.0 + total_time / 10.0)  # 时间越短分数越高
        
        priority = self.staleness_weight * staleness_score + self.time_weight * time_score
        
        logging.debug(f"客户端 {client_id}: staleness={staleness}, total_time={total_time:.2f}, "
                     f"priority={priority:.4f}")
        
        return priority

    # 此处做了修改，具体为：新增贪心客户端选择算法
    def greedy_client_selection(self, available_clients: list, num_select: int) -> list:
        """贪心客户端选择算法"""
        if not available_clients:
            return []
        
        # 计算所有可用客户端的优先级
        client_priorities = []
        for client_id in available_clients:
            priority = self.calculate_client_priority(client_id)
            if priority > 0:  # 只考虑优先级大于0的客户端
                client_priorities.append((client_id, priority))
        
        # 按优先级降序排序
        client_priorities.sort(key=lambda x: x[1], reverse=True)
        
        # 选择前num_select个客户端
        selected_clients = [client_id for client_id, _ in client_priorities[:num_select]]
        
        logging.info(f"贪心选择结果: 从{len(available_clients)}个可用客户端中选择了{len(selected_clients)}个")
        for client_id, priority in client_priorities[:num_select]:
            staleness = self.calculate_client_staleness(client_id)
            comp_time = self.estimate_computation_time(client_id)
            comm_time = self.estimate_communication_time(client_id)
            logging.info(f"  客户端 {client_id}: priority={priority:.4f}, staleness={staleness}, "
                        f"est_time={comp_time+comm_time:.2f}s")
        
        return selected_clients

    # 此处做了修改，具体为：重写客户端选择方法，使用贪心算法替代随机选择
    async def choose_clients(self, for_next_batch=False):
        """选择客户端进行训练（重写父类方法）"""
        # 获取可用客户端列表
        available_clients = list(self.clients.keys())

        if not available_clients:
            logging.warning("没有可用的客户端")
            return []

        # 确定要选择的客户端数量
        if for_next_batch:
            num_clients = min(len(available_clients), self.clients_per_round)
        else:
            num_clients = min(len(available_clients), self.clients_per_round)

        # 使用贪心算法选择客户端
        selected_clients = self.greedy_client_selection(available_clients, num_clients)

        logging.info(fonts.colourize(f"[{self}] 使用RESCAFL贪心算法选择了 {len(selected_clients)} 个客户端"))

        return selected_clients

    # 此处做了修改，具体为：新增客户端性能指标更新方法
    def update_client_metrics(self, client_id: int, computation_time: float,
                            communication_time: float):
        """更新客户端性能指标"""
        # 更新计算时间记录
        if client_id not in self.client_computation_times:
            self.client_computation_times[client_id] = []
        self.client_computation_times[client_id].append(computation_time)

        # 保持最近10次记录
        if len(self.client_computation_times[client_id]) > 10:
            self.client_computation_times[client_id] = self.client_computation_times[client_id][-10:]

        # 更新通信时间记录
        if client_id not in self.client_communication_times:
            self.client_communication_times[client_id] = []
        self.client_communication_times[client_id].append(communication_time)

        # 保持最近10次记录
        if len(self.client_communication_times[client_id]) > 10:
            self.client_communication_times[client_id] = self.client_communication_times[client_id][-10:]

        # 更新最后更新轮次
        self.client_last_update_round[client_id] = self.current_round

        logging.debug(f"更新客户端 {client_id} 指标: comp_time={computation_time:.2f}s, "
                     f"comm_time={communication_time:.2f}s")

    async def aggregate_weights(self, updates, baseline_weights, weights_received):
        """Process the client reports by aggregating their weights."""
        # Calculate the new mixing hyperparameter with client's staleness

        # Reset the list of losses for this round

        # Perform weighted averaging
        for i, update in enumerate(updates):
            report = update.report
            # Collect loss information if available
            if hasattr(report, 'final_loss'):
                client_id = updates[i].client_id
                loss = report.final_loss

                # Store loss for this client
                self.current_round_losses.append(loss)

                logging.info(f"[Server] Client {client_id} reported loss: {loss}")

            if self.adaptive_mixing:
                mixing_hyperparam = self.mixing_hyperparam * self._staleness_function(update.staleness)
            else:
                mixing_hyperparam = self.mixing_hyperparam

            logging.info("[client %s], staleness: %s, mixing hyperparameters: %s",
                         i, update.staleness, mixing_hyperparam)

            baseline_weights = await self.algorithm.aggregate_weights(
                baseline_weights, weights_received[i], mixing=mixing_hyperparam)

            # 此处做了修改，具体为：更新客户端性能指标
            if hasattr(report, 'actual_computation_time') and hasattr(report, 'actual_communication_time'):
                self.update_client_metrics(update.client_id,
                                         report.actual_computation_time,
                                         report.actual_communication_time)

        # Calculate average loss for this round if we have any losses
        if self.current_round_losses:
            self.avg_loss = statistics.mean(self.current_round_losses)
            logging.info(f"[Server] Average loss for round {self.current_round}: {self.avg_loss}")

        return baseline_weights

    @staticmethod
    def _staleness_function(staleness) -> float:
        """Staleness function used to adjust the mixing hyperparameter"""
        if hasattr(Config().server, "staleness_weighting_function"):
            staleness_func_param = Config().server.staleness_weighting_function
            func_type = staleness_func_param.type.lower()
            if func_type == "constant":
                return Server._constant_function()
            elif func_type == "polynomial":
                a = staleness_func_param.pa
                return Server._polynomial_function(staleness, a)
            elif func_type == "hinge":
                a = staleness_func_param.ha
                b = staleness_func_param.hb
                return Server._hinge_function(staleness, a, b)
            else:
                logging.warning(
                    "FedAsync: Unknown staleness weighting function type. "
                    "Type needs to be constant, polynomial, or hinge."
                )
        else:
            return Server._constant_function()

    @staticmethod
    def _constant_function() -> float:
        """Constant staleness function as proposed in Sec. 5.2, Evaluation Setup."""
        return 1

    @staticmethod
    def _polynomial_function(staleness, a) -> float:
        """Polynomial staleness function as proposed in Sec. 5.2, Evaluation Setup."""
        return (staleness + 1) ** -a

    @staticmethod
    def _hinge_function(staleness, a, b) -> float:
        """Hinge staleness function as proposed in Sec. 5.2, Evaluation Setup."""
        if staleness <= b:
            return 1
        else:
            return 1 / (a * (staleness - b) + 1)
