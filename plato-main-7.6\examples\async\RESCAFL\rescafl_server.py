"""
RESCAFL (Resource-Efficient Staleness-aware Client-Adaptive Federated Learning) 服务器实现。

主要特性：
1. 服务器端计算和通信时间估算
2. 基于陈旧度和运行时间的贪心客户端选择
3. 继承Plato的Socket.IO通信机制
"""

import logging
import math
import os
import random
import time
import asyncio
import statistics
from collections import OrderedDict, defaultdict
from typing import Dict, List, Tuple, Optional

from plato.config import Config
from plato.servers import fedavg
from plato.utils import fonts


class Server(fedavg.Server):
    """RESCAFL联邦学习服务器，继承自FedAvg服务器"""

    def __init__(
        self, model=None, datasource=None, algorithm=None, trainer=None, callbacks=None
    ):
        super().__init__(
            model=model,
            datasource=datasource,
            algorithm=algorithm,
            trainer=trainer,
            callbacks=callbacks,
        )

        # RESCAFL特有参数
        self.client_computation_times = {}  # 客户端计算时间记录
        self.client_communication_times = {}  # 客户端通信时间记录
        self.client_staleness = {}  # 客户端陈旧度记录
        self.client_last_update_round = {}  # 客户端最后更新轮次
        self.client_capabilities = {}  # 客户端计算能力
        self.client_bandwidth = {}  # 客户端带宽
        
        # 时间估算参数
        self.base_computation_time = 10.0  # 基础计算时间（秒）
        self.base_communication_time = 2.0  # 基础通信时间（秒）
        self.model_size_mb = 5.0  # 模型大小（MB）
        
        # 贪心选择参数
        self.staleness_weight = 0.5  # 陈旧度权重
        self.time_weight = 0.5  # 时间权重
        self.max_staleness = 10  # 最大允许陈旧度
        
        logging.info("RESCAFL服务器初始化完成")

    def configure(self) -> None:
        """配置RESCAFL服务器参数"""
        super().configure()
        
        config = Config()
        
        # 从配置文件读取RESCAFL参数
        if hasattr(config.server, 'staleness_weight'):
            self.staleness_weight = config.server.staleness_weight
        if hasattr(config.server, 'time_weight'):
            self.time_weight = config.server.time_weight
        if hasattr(config.server, 'max_staleness'):
            self.max_staleness = config.server.max_staleness
        if hasattr(config.server, 'base_computation_time'):
            self.base_computation_time = config.server.base_computation_time
        if hasattr(config.server, 'base_communication_time'):
            self.base_communication_time = config.server.base_communication_time
        if hasattr(config.server, 'model_size_mb'):
            self.model_size_mb = config.server.model_size_mb

        logging.info(f"RESCAFL参数配置: staleness_weight={self.staleness_weight}, "
                    f"time_weight={self.time_weight}, max_staleness={self.max_staleness}")

    def estimate_computation_time(self, client_id: int) -> float:
        """估算客户端计算时间"""
        # 获取客户端计算能力（如果没有记录，使用随机值模拟）
        if client_id not in self.client_capabilities:
            # 模拟不同客户端的计算能力差异（0.5-2.0倍基础时间）
            self.client_capabilities[client_id] = random.uniform(0.5, 2.0)
        
        capability_factor = self.client_capabilities[client_id]
        
        # 基于历史记录调整估算
        if client_id in self.client_computation_times:
            historical_times = self.client_computation_times[client_id]
            if historical_times:
                avg_historical = statistics.mean(historical_times[-5:])  # 使用最近5次的平均值
                estimated_time = 0.7 * avg_historical + 0.3 * (self.base_computation_time * capability_factor)
            else:
                estimated_time = self.base_computation_time * capability_factor
        else:
            estimated_time = self.base_computation_time * capability_factor
            
        return max(1.0, estimated_time)  # 最小1秒

    def estimate_communication_time(self, client_id: int) -> float:
        """估算客户端通信时间"""
        # 获取客户端带宽（如果没有记录，使用随机值模拟）
        if client_id not in self.client_bandwidth:
            # 模拟不同客户端的带宽差异（0.5-3.0倍基础时间）
            self.client_bandwidth[client_id] = random.uniform(0.5, 3.0)
        
        bandwidth_factor = self.client_bandwidth[client_id]
        
        # 基于模型大小和带宽估算通信时间
        estimated_time = (self.model_size_mb * 2) / bandwidth_factor  # 上传+下载
        
        # 基于历史记录调整估算
        if client_id in self.client_communication_times:
            historical_times = self.client_communication_times[client_id]
            if historical_times:
                avg_historical = statistics.mean(historical_times[-5:])
                estimated_time = 0.6 * avg_historical + 0.4 * estimated_time
                
        return max(0.5, estimated_time)  # 最小0.5秒

    def calculate_client_staleness(self, client_id: int) -> int:
        """计算客户端陈旧度"""
        if client_id not in self.client_last_update_round:
            return 0
        
        staleness = self.current_round - self.client_last_update_round[client_id]
        return max(0, staleness)

    def calculate_client_priority(self, client_id: int) -> float:
        """计算客户端优先级（用于贪心选择）"""
        # 计算陈旧度
        staleness = self.calculate_client_staleness(client_id)
        
        # 如果陈旧度超过最大值，优先级为0
        if staleness > self.max_staleness:
            return 0.0
        
        # 估算总运行时间
        comp_time = self.estimate_computation_time(client_id)
        comm_time = self.estimate_communication_time(client_id)
        total_time = comp_time + comm_time
        
        # 计算优先级：陈旧度越高优先级越高，时间越短优先级越高
        staleness_score = staleness / (self.max_staleness + 1)  # 归一化到[0,1)
        time_score = 1.0 / (1.0 + total_time / 10.0)  # 时间越短分数越高
        
        priority = self.staleness_weight * staleness_score + self.time_weight * time_score
        
        logging.debug(f"客户端 {client_id}: staleness={staleness}, total_time={total_time:.2f}, "
                     f"priority={priority:.4f}")
        
        return priority

    def greedy_client_selection(self, available_clients: List[int], num_select: int) -> List[int]:
        """贪心客户端选择算法"""
        if not available_clients:
            return []
        
        # 计算所有可用客户端的优先级
        client_priorities = []
        for client_id in available_clients:
            priority = self.calculate_client_priority(client_id)
            if priority > 0:  # 只考虑优先级大于0的客户端
                client_priorities.append((client_id, priority))
        
        # 按优先级降序排序
        client_priorities.sort(key=lambda x: x[1], reverse=True)
        
        # 选择前num_select个客户端
        selected_clients = [client_id for client_id, _ in client_priorities[:num_select]]
        
        logging.info(f"贪心选择结果: 从{len(available_clients)}个可用客户端中选择了{len(selected_clients)}个")
        for client_id, priority in client_priorities[:num_select]:
            staleness = self.calculate_client_staleness(client_id)
            comp_time = self.estimate_computation_time(client_id)
            comm_time = self.estimate_communication_time(client_id)
            logging.info(f"  客户端 {client_id}: priority={priority:.4f}, staleness={staleness}, "
                        f"est_time={comp_time+comm_time:.2f}s")
        
        return selected_clients

    async def choose_clients(self, for_next_batch=False):
        """选择客户端进行训练（重写父类方法）"""
        # 获取可用客户端列表
        available_clients = list(self.clients.keys())
        
        if not available_clients:
            logging.warning("没有可用的客户端")
            return []
        
        # 确定要选择的客户端数量
        if for_next_batch:
            num_clients = min(len(available_clients), self.clients_per_round)
        else:
            num_clients = min(len(available_clients), self.clients_per_round)
        
        # 使用贪心算法选择客户端
        selected_clients = self.greedy_client_selection(available_clients, num_clients)
        
        logging.info(fonts.colourize(f"[{self}] 使用RESCAFL贪心算法选择了 {len(selected_clients)} 个客户端"))
        
        return selected_clients

    def update_client_metrics(self, client_id: int, computation_time: float, 
                            communication_time: float):
        """更新客户端性能指标"""
        # 更新计算时间记录
        if client_id not in self.client_computation_times:
            self.client_computation_times[client_id] = []
        self.client_computation_times[client_id].append(computation_time)
        
        # 保持最近10次记录
        if len(self.client_computation_times[client_id]) > 10:
            self.client_computation_times[client_id] = self.client_computation_times[client_id][-10:]
        
        # 更新通信时间记录
        if client_id not in self.client_communication_times:
            self.client_communication_times[client_id] = []
        self.client_communication_times[client_id].append(communication_time)
        
        # 保持最近10次记录
        if len(self.client_communication_times[client_id]) > 10:
            self.client_communication_times[client_id] = self.client_communication_times[client_id][-10:]
        
        # 更新最后更新轮次
        self.client_last_update_round[client_id] = self.current_round
        
        logging.debug(f"更新客户端 {client_id} 指标: comp_time={computation_time:.2f}s, "
                     f"comm_time={communication_time:.2f}s")

    def get_logged_items(self) -> dict:
        """获取要记录的日志项目"""
        logged_items = super().get_logged_items()
        
        # 添加RESCAFL特有的指标
        if self.updates:
            staleness_values = [self.calculate_client_staleness(update.client_id) 
                              for update in self.updates]
            logged_items["avg_staleness"] = statistics.mean(staleness_values) if staleness_values else 0
            logged_items["max_staleness"] = max(staleness_values) if staleness_values else 0
            logged_items["min_staleness"] = min(staleness_values) if staleness_values else 0
            
            # 计算平均估算时间
            comp_times = [self.estimate_computation_time(update.client_id) for update in self.updates]
            comm_times = [self.estimate_communication_time(update.client_id) for update in self.updates]
            logged_items["avg_est_comp_time"] = statistics.mean(comp_times) if comp_times else 0
            logged_items["avg_est_comm_time"] = statistics.mean(comm_times) if comm_times else 0
            logged_items["avg_est_total_time"] = statistics.mean([c+t for c,t in zip(comp_times, comm_times)]) if comp_times else 0
        else:
            logged_items["avg_staleness"] = 0
            logged_items["max_staleness"] = 0
            logged_items["min_staleness"] = 0
            logged_items["avg_est_comp_time"] = 0
            logged_items["avg_est_comm_time"] = 0
            logged_items["avg_est_total_time"] = 0
        
        return logged_items
